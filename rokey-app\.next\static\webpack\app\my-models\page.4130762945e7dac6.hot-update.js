"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/page",{

/***/ "(app-pages-browser)/./src/lib/stripe-client.ts":
/*!**********************************!*\
  !*** ./src/lib/stripe-client.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TIER_CONFIGS: () => (/* binding */ TIER_CONFIGS),\n/* harmony export */   canPerformAction: () => (/* binding */ canPerformAction),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   getPriceIdForTier: () => (/* binding */ getPriceIdForTier),\n/* harmony export */   getTierConfig: () => (/* binding */ getTierConfig),\n/* harmony export */   getTierFromPriceId: () => (/* binding */ getTierFromPriceId),\n/* harmony export */   hasFeatureAccess: () => (/* binding */ hasFeatureAccess)\n/* harmony export */ });\n/* harmony import */ var _stripe_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./stripe-config */ \"(app-pages-browser)/./src/lib/stripe-config.ts\");\n// Client-safe Stripe utilities (no server-side Stripe instance)\n\nconst TIER_CONFIGS = {\n    free: {\n        name: 'Free',\n        price: '$0',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.FREE,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.FREE,\n        features: [\n            'Unlimited API requests',\n            '1 Custom Configuration',\n            '3 API Keys per config',\n            'All 300+ AI models',\n            'Strict fallback routing only',\n            'Basic analytics only',\n            'No custom roles, basic router only',\n            'Limited logs',\n            'Community support'\n        ],\n        limits: {\n            configurations: 1,\n            apiKeysPerConfig: 3,\n            apiRequests: 999999,\n            canUseAdvancedRouting: false,\n            canUseCustomRoles: false,\n            maxCustomRoles: 0,\n            canUsePromptEngineering: false,\n            canUseKnowledgeBase: false,\n            knowledgeBaseDocuments: 0,\n            canUseSemanticCaching: false\n        }\n    },\n    starter: {\n        name: 'Starter',\n        price: '$15',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.STARTER,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.STARTER,\n        features: [\n            'Unlimited API requests',\n            '15 Custom Configurations',\n            '5 API Keys per config',\n            'All 300+ AI models',\n            'Intelligent routing strategies',\n            'Up to 3 custom roles',\n            'Intelligent role routing',\n            'Prompt engineering (no file upload)',\n            'Enhanced logs and analytics',\n            'Community support'\n        ],\n        limits: {\n            configurations: 15,\n            apiKeysPerConfig: 5,\n            apiRequests: 999999,\n            canUseAdvancedRouting: true,\n            canUseCustomRoles: true,\n            maxCustomRoles: 3,\n            canUsePromptEngineering: true,\n            canUseKnowledgeBase: false,\n            knowledgeBaseDocuments: 0,\n            canUseSemanticCaching: false\n        }\n    },\n    professional: {\n        name: 'Professional',\n        price: '$50',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.PROFESSIONAL,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.PROFESSIONAL,\n        features: [\n            'Unlimited API requests',\n            'Unlimited Custom Configurations',\n            'Unlimited API Keys per config',\n            'All 300+ AI models',\n            'All advanced routing strategies',\n            'Unlimited custom roles',\n            'Prompt engineering + Knowledge base (5 documents)',\n            'Semantic caching',\n            'Advanced analytics and logging',\n            'Priority email support'\n        ],\n        limits: {\n            configurations: 999999,\n            apiKeysPerConfig: 999999,\n            apiRequests: 999999,\n            canUseAdvancedRouting: true,\n            canUseCustomRoles: true,\n            maxCustomRoles: 999999,\n            canUsePromptEngineering: true,\n            canUseKnowledgeBase: true,\n            knowledgeBaseDocuments: 5,\n            canUseSemanticCaching: true\n        }\n    },\n    enterprise: {\n        name: 'Enterprise',\n        price: '$149',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.ENTERPRISE,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.ENTERPRISE,\n        features: [\n            'Unlimited API requests',\n            'Unlimited configurations',\n            'Unlimited API keys',\n            'All 300+ models + priority access',\n            'All routing strategies',\n            'Unlimited custom roles',\n            'All features + priority support',\n            'Unlimited knowledge base documents',\n            'Advanced semantic caching',\n            'Custom integrations',\n            'Dedicated support + phone',\n            'SLA guarantee'\n        ],\n        limits: {\n            configurations: 999999,\n            apiKeysPerConfig: 999999,\n            apiRequests: 999999,\n            canUseAdvancedRouting: true,\n            canUseCustomRoles: true,\n            maxCustomRoles: 999999,\n            canUsePromptEngineering: true,\n            canUseKnowledgeBase: true,\n            knowledgeBaseDocuments: 999999,\n            canUseSemanticCaching: true\n        }\n    }\n};\nfunction getTierConfig(tier) {\n    return TIER_CONFIGS[tier];\n}\nfunction getPriceIdForTier(tier) {\n    return TIER_CONFIGS[tier].priceId;\n}\nfunction getTierFromPriceId(priceId) {\n    for (const [tier, config] of Object.entries(TIER_CONFIGS)){\n        if (config.priceId === priceId) {\n            return tier;\n        }\n    }\n    return 'free'; // Default fallback to free tier\n}\nfunction formatPrice(tier) {\n    return TIER_CONFIGS[tier].price;\n}\nfunction canPerformAction(tier, action, currentCount) {\n    const limits = TIER_CONFIGS[tier].limits;\n    switch(action){\n        case 'create_config':\n            return currentCount < limits.configurations;\n        case 'create_api_key':\n            return currentCount < limits.apiKeysPerConfig;\n        default:\n            return true;\n    }\n}\nfunction hasFeatureAccess(tier, feature) {\n    const limits = TIER_CONFIGS[tier].limits;\n    switch(feature){\n        case 'custom_roles':\n            return limits.canUseCustomRoles;\n        case 'knowledge_base':\n            return limits.canUseKnowledgeBase;\n        case 'advanced_routing':\n            return limits.canUseAdvancedRouting;\n        case 'prompt_engineering':\n            return limits.canUsePromptEngineering;\n        case 'semantic_caching':\n            return limits.canUseSemanticCaching;\n        case 'configurations':\n            return limits.configurations > 0; // All tiers can create at least some configurations\n        default:\n            return false;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvc3RyaXBlLWNsaWVudC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFBLGdFQUFnRTtBQUNPO0FBd0JoRSxNQUFNRSxlQUFxRDtJQUNoRUMsTUFBTTtRQUNKQyxNQUFNO1FBQ05DLE9BQU87UUFDUEMsU0FBU04sNERBQWdCQSxDQUFDTyxJQUFJO1FBQzlCQyxXQUFXUCw4REFBa0JBLENBQUNNLElBQUk7UUFDbENFLFVBQVU7WUFDUjtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7U0FDRDtRQUNEQyxRQUFRO1lBQ05DLGdCQUFnQjtZQUNoQkMsa0JBQWtCO1lBQ2xCQyxhQUFhO1lBQ2JDLHVCQUF1QjtZQUN2QkMsbUJBQW1CO1lBQ25CQyxnQkFBZ0I7WUFDaEJDLHlCQUF5QjtZQUN6QkMscUJBQXFCO1lBQ3JCQyx3QkFBd0I7WUFDeEJDLHVCQUF1QjtRQUN6QjtJQUNGO0lBQ0FDLFNBQVM7UUFDUGpCLE1BQU07UUFDTkMsT0FBTztRQUNQQyxTQUFTTiw0REFBZ0JBLENBQUNzQixPQUFPO1FBQ2pDZCxXQUFXUCw4REFBa0JBLENBQUNxQixPQUFPO1FBQ3JDYixVQUFVO1lBQ1I7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7U0FDRDtRQUNEQyxRQUFRO1lBQ05DLGdCQUFnQjtZQUNoQkMsa0JBQWtCO1lBQ2xCQyxhQUFhO1lBQ2JDLHVCQUF1QjtZQUN2QkMsbUJBQW1CO1lBQ25CQyxnQkFBZ0I7WUFDaEJDLHlCQUF5QjtZQUN6QkMscUJBQXFCO1lBQ3JCQyx3QkFBd0I7WUFDeEJDLHVCQUF1QjtRQUN6QjtJQUNGO0lBQ0FHLGNBQWM7UUFDWm5CLE1BQU07UUFDTkMsT0FBTztRQUNQQyxTQUFTTiw0REFBZ0JBLENBQUN3QixZQUFZO1FBQ3RDaEIsV0FBV1AsOERBQWtCQSxDQUFDdUIsWUFBWTtRQUMxQ2YsVUFBVTtZQUNSO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7UUFDREMsUUFBUTtZQUNOQyxnQkFBZ0I7WUFDaEJDLGtCQUFrQjtZQUNsQkMsYUFBYTtZQUNiQyx1QkFBdUI7WUFDdkJDLG1CQUFtQjtZQUNuQkMsZ0JBQWdCO1lBQ2hCQyx5QkFBeUI7WUFDekJDLHFCQUFxQjtZQUNyQkMsd0JBQXdCO1lBQ3hCQyx1QkFBdUI7UUFDekI7SUFDRjtJQUNBSyxZQUFZO1FBQ1ZyQixNQUFNO1FBQ05DLE9BQU87UUFDUEMsU0FBU04sNERBQWdCQSxDQUFDMEIsVUFBVTtRQUNwQ2xCLFdBQVdQLDhEQUFrQkEsQ0FBQ3lCLFVBQVU7UUFDeENqQixVQUFVO1lBQ1I7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7UUFDREMsUUFBUTtZQUNOQyxnQkFBZ0I7WUFDaEJDLGtCQUFrQjtZQUNsQkMsYUFBYTtZQUNiQyx1QkFBdUI7WUFDdkJDLG1CQUFtQjtZQUNuQkMsZ0JBQWdCO1lBQ2hCQyx5QkFBeUI7WUFDekJDLHFCQUFxQjtZQUNyQkMsd0JBQXdCO1lBQ3hCQyx1QkFBdUI7UUFDekI7SUFDRjtBQUNGLEVBQUU7QUFFSyxTQUFTTyxjQUFjQyxJQUFzQjtJQUNsRCxPQUFPMUIsWUFBWSxDQUFDMEIsS0FBSztBQUMzQjtBQUVPLFNBQVNDLGtCQUFrQkQsSUFBc0I7SUFDdEQsT0FBTzFCLFlBQVksQ0FBQzBCLEtBQUssQ0FBQ3RCLE9BQU87QUFDbkM7QUFFTyxTQUFTd0IsbUJBQW1CeEIsT0FBZTtJQUNoRCxLQUFLLE1BQU0sQ0FBQ3NCLE1BQU1HLE9BQU8sSUFBSUMsT0FBT0MsT0FBTyxDQUFDL0IsY0FBZTtRQUN6RCxJQUFJNkIsT0FBT3pCLE9BQU8sS0FBS0EsU0FBUztZQUM5QixPQUFPc0I7UUFDVDtJQUNGO0lBQ0EsT0FBTyxRQUFRLGdDQUFnQztBQUNqRDtBQUVPLFNBQVNNLFlBQVlOLElBQXNCO0lBQ2hELE9BQU8xQixZQUFZLENBQUMwQixLQUFLLENBQUN2QixLQUFLO0FBQ2pDO0FBRU8sU0FBUzhCLGlCQUNkUCxJQUFzQixFQUN0QlEsTUFBMEMsRUFDMUNDLFlBQW9CO0lBRXBCLE1BQU0zQixTQUFTUixZQUFZLENBQUMwQixLQUFLLENBQUNsQixNQUFNO0lBRXhDLE9BQVEwQjtRQUNOLEtBQUs7WUFDSCxPQUFPQyxlQUFlM0IsT0FBT0MsY0FBYztRQUM3QyxLQUFLO1lBQ0gsT0FBTzBCLGVBQWUzQixPQUFPRSxnQkFBZ0I7UUFDL0M7WUFDRSxPQUFPO0lBQ1g7QUFDRjtBQUVPLFNBQVMwQixpQkFDZFYsSUFBc0IsRUFDdEJXLE9BQThIO0lBRTlILE1BQU03QixTQUFTUixZQUFZLENBQUMwQixLQUFLLENBQUNsQixNQUFNO0lBRXhDLE9BQVE2QjtRQUNOLEtBQUs7WUFDSCxPQUFPN0IsT0FBT0ssaUJBQWlCO1FBQ2pDLEtBQUs7WUFDSCxPQUFPTCxPQUFPUSxtQkFBbUI7UUFDbkMsS0FBSztZQUNILE9BQU9SLE9BQU9JLHFCQUFxQjtRQUNyQyxLQUFLO1lBQ0gsT0FBT0osT0FBT08sdUJBQXVCO1FBQ3ZDLEtBQUs7WUFDSCxPQUFPUCxPQUFPVSxxQkFBcUI7UUFDckMsS0FBSztZQUNILE9BQU9WLE9BQU9DLGNBQWMsR0FBRyxHQUFHLG9EQUFvRDtRQUN4RjtZQUNFLE9BQU87SUFDWDtBQUNGIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcbGliXFxzdHJpcGUtY2xpZW50LnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENsaWVudC1zYWZlIFN0cmlwZSB1dGlsaXRpZXMgKG5vIHNlcnZlci1zaWRlIFN0cmlwZSBpbnN0YW5jZSlcbmltcG9ydCB7IFNUUklQRV9QUklDRV9JRFMsIFNUUklQRV9QUk9EVUNUX0lEUyB9IGZyb20gJy4vc3RyaXBlLWNvbmZpZyc7XG5cbmV4cG9ydCB0eXBlIFN1YnNjcmlwdGlvblRpZXIgPSAnZnJlZScgfCAnc3RhcnRlcicgfCAncHJvZmVzc2lvbmFsJyB8ICdlbnRlcnByaXNlJztcblxuZXhwb3J0IGludGVyZmFjZSBUaWVyQ29uZmlnIHtcbiAgbmFtZTogc3RyaW5nO1xuICBwcmljZTogc3RyaW5nO1xuICBwcmljZUlkOiBzdHJpbmcgfCBudWxsOyAvLyBudWxsIGZvciBmcmVlIHRpZXJcbiAgcHJvZHVjdElkOiBzdHJpbmcgfCBudWxsOyAvLyBudWxsIGZvciBmcmVlIHRpZXJcbiAgZmVhdHVyZXM6IHN0cmluZ1tdO1xuICBsaW1pdHM6IHtcbiAgICBjb25maWd1cmF0aW9uczogbnVtYmVyO1xuICAgIGFwaUtleXNQZXJDb25maWc6IG51bWJlcjtcbiAgICBhcGlSZXF1ZXN0czogbnVtYmVyO1xuICAgIGNhblVzZUFkdmFuY2VkUm91dGluZzogYm9vbGVhbjtcbiAgICBjYW5Vc2VDdXN0b21Sb2xlczogYm9vbGVhbjtcbiAgICBtYXhDdXN0b21Sb2xlczogbnVtYmVyO1xuICAgIGNhblVzZVByb21wdEVuZ2luZWVyaW5nOiBib29sZWFuO1xuICAgIGNhblVzZUtub3dsZWRnZUJhc2U6IGJvb2xlYW47XG4gICAga25vd2xlZGdlQmFzZURvY3VtZW50czogbnVtYmVyO1xuICAgIGNhblVzZVNlbWFudGljQ2FjaGluZzogYm9vbGVhbjtcbiAgfTtcbn1cblxuZXhwb3J0IGNvbnN0IFRJRVJfQ09ORklHUzogUmVjb3JkPFN1YnNjcmlwdGlvblRpZXIsIFRpZXJDb25maWc+ID0ge1xuICBmcmVlOiB7XG4gICAgbmFtZTogJ0ZyZWUnLFxuICAgIHByaWNlOiAnJDAnLFxuICAgIHByaWNlSWQ6IFNUUklQRV9QUklDRV9JRFMuRlJFRSxcbiAgICBwcm9kdWN0SWQ6IFNUUklQRV9QUk9EVUNUX0lEUy5GUkVFLFxuICAgIGZlYXR1cmVzOiBbXG4gICAgICAnVW5saW1pdGVkIEFQSSByZXF1ZXN0cycsXG4gICAgICAnMSBDdXN0b20gQ29uZmlndXJhdGlvbicsXG4gICAgICAnMyBBUEkgS2V5cyBwZXIgY29uZmlnJyxcbiAgICAgICdBbGwgMzAwKyBBSSBtb2RlbHMnLFxuICAgICAgJ1N0cmljdCBmYWxsYmFjayByb3V0aW5nIG9ubHknLFxuICAgICAgJ0Jhc2ljIGFuYWx5dGljcyBvbmx5JyxcbiAgICAgICdObyBjdXN0b20gcm9sZXMsIGJhc2ljIHJvdXRlciBvbmx5JyxcbiAgICAgICdMaW1pdGVkIGxvZ3MnLFxuICAgICAgJ0NvbW11bml0eSBzdXBwb3J0JyxcbiAgICBdLFxuICAgIGxpbWl0czoge1xuICAgICAgY29uZmlndXJhdGlvbnM6IDEsXG4gICAgICBhcGlLZXlzUGVyQ29uZmlnOiAzLFxuICAgICAgYXBpUmVxdWVzdHM6IDk5OTk5OSxcbiAgICAgIGNhblVzZUFkdmFuY2VkUm91dGluZzogZmFsc2UsXG4gICAgICBjYW5Vc2VDdXN0b21Sb2xlczogZmFsc2UsXG4gICAgICBtYXhDdXN0b21Sb2xlczogMCxcbiAgICAgIGNhblVzZVByb21wdEVuZ2luZWVyaW5nOiBmYWxzZSxcbiAgICAgIGNhblVzZUtub3dsZWRnZUJhc2U6IGZhbHNlLFxuICAgICAga25vd2xlZGdlQmFzZURvY3VtZW50czogMCxcbiAgICAgIGNhblVzZVNlbWFudGljQ2FjaGluZzogZmFsc2UsXG4gICAgfSxcbiAgfSxcbiAgc3RhcnRlcjoge1xuICAgIG5hbWU6ICdTdGFydGVyJyxcbiAgICBwcmljZTogJyQxNScsXG4gICAgcHJpY2VJZDogU1RSSVBFX1BSSUNFX0lEUy5TVEFSVEVSLFxuICAgIHByb2R1Y3RJZDogU1RSSVBFX1BST0RVQ1RfSURTLlNUQVJURVIsXG4gICAgZmVhdHVyZXM6IFtcbiAgICAgICdVbmxpbWl0ZWQgQVBJIHJlcXVlc3RzJyxcbiAgICAgICcxNSBDdXN0b20gQ29uZmlndXJhdGlvbnMnLFxuICAgICAgJzUgQVBJIEtleXMgcGVyIGNvbmZpZycsXG4gICAgICAnQWxsIDMwMCsgQUkgbW9kZWxzJyxcbiAgICAgICdJbnRlbGxpZ2VudCByb3V0aW5nIHN0cmF0ZWdpZXMnLFxuICAgICAgJ1VwIHRvIDMgY3VzdG9tIHJvbGVzJyxcbiAgICAgICdJbnRlbGxpZ2VudCByb2xlIHJvdXRpbmcnLFxuICAgICAgJ1Byb21wdCBlbmdpbmVlcmluZyAobm8gZmlsZSB1cGxvYWQpJyxcbiAgICAgICdFbmhhbmNlZCBsb2dzIGFuZCBhbmFseXRpY3MnLFxuICAgICAgJ0NvbW11bml0eSBzdXBwb3J0JyxcbiAgICBdLFxuICAgIGxpbWl0czoge1xuICAgICAgY29uZmlndXJhdGlvbnM6IDE1LFxuICAgICAgYXBpS2V5c1BlckNvbmZpZzogNSxcbiAgICAgIGFwaVJlcXVlc3RzOiA5OTk5OTksXG4gICAgICBjYW5Vc2VBZHZhbmNlZFJvdXRpbmc6IHRydWUsXG4gICAgICBjYW5Vc2VDdXN0b21Sb2xlczogdHJ1ZSxcbiAgICAgIG1heEN1c3RvbVJvbGVzOiAzLFxuICAgICAgY2FuVXNlUHJvbXB0RW5naW5lZXJpbmc6IHRydWUsXG4gICAgICBjYW5Vc2VLbm93bGVkZ2VCYXNlOiBmYWxzZSxcbiAgICAgIGtub3dsZWRnZUJhc2VEb2N1bWVudHM6IDAsXG4gICAgICBjYW5Vc2VTZW1hbnRpY0NhY2hpbmc6IGZhbHNlLFxuICAgIH0sXG4gIH0sXG4gIHByb2Zlc3Npb25hbDoge1xuICAgIG5hbWU6ICdQcm9mZXNzaW9uYWwnLFxuICAgIHByaWNlOiAnJDUwJyxcbiAgICBwcmljZUlkOiBTVFJJUEVfUFJJQ0VfSURTLlBST0ZFU1NJT05BTCxcbiAgICBwcm9kdWN0SWQ6IFNUUklQRV9QUk9EVUNUX0lEUy5QUk9GRVNTSU9OQUwsXG4gICAgZmVhdHVyZXM6IFtcbiAgICAgICdVbmxpbWl0ZWQgQVBJIHJlcXVlc3RzJyxcbiAgICAgICdVbmxpbWl0ZWQgQ3VzdG9tIENvbmZpZ3VyYXRpb25zJyxcbiAgICAgICdVbmxpbWl0ZWQgQVBJIEtleXMgcGVyIGNvbmZpZycsXG4gICAgICAnQWxsIDMwMCsgQUkgbW9kZWxzJyxcbiAgICAgICdBbGwgYWR2YW5jZWQgcm91dGluZyBzdHJhdGVnaWVzJyxcbiAgICAgICdVbmxpbWl0ZWQgY3VzdG9tIHJvbGVzJyxcbiAgICAgICdQcm9tcHQgZW5naW5lZXJpbmcgKyBLbm93bGVkZ2UgYmFzZSAoNSBkb2N1bWVudHMpJyxcbiAgICAgICdTZW1hbnRpYyBjYWNoaW5nJyxcbiAgICAgICdBZHZhbmNlZCBhbmFseXRpY3MgYW5kIGxvZ2dpbmcnLFxuICAgICAgJ1ByaW9yaXR5IGVtYWlsIHN1cHBvcnQnLFxuICAgIF0sXG4gICAgbGltaXRzOiB7XG4gICAgICBjb25maWd1cmF0aW9uczogOTk5OTk5LFxuICAgICAgYXBpS2V5c1BlckNvbmZpZzogOTk5OTk5LFxuICAgICAgYXBpUmVxdWVzdHM6IDk5OTk5OSxcbiAgICAgIGNhblVzZUFkdmFuY2VkUm91dGluZzogdHJ1ZSxcbiAgICAgIGNhblVzZUN1c3RvbVJvbGVzOiB0cnVlLFxuICAgICAgbWF4Q3VzdG9tUm9sZXM6IDk5OTk5OSxcbiAgICAgIGNhblVzZVByb21wdEVuZ2luZWVyaW5nOiB0cnVlLFxuICAgICAgY2FuVXNlS25vd2xlZGdlQmFzZTogdHJ1ZSxcbiAgICAgIGtub3dsZWRnZUJhc2VEb2N1bWVudHM6IDUsXG4gICAgICBjYW5Vc2VTZW1hbnRpY0NhY2hpbmc6IHRydWUsXG4gICAgfSxcbiAgfSxcbiAgZW50ZXJwcmlzZToge1xuICAgIG5hbWU6ICdFbnRlcnByaXNlJyxcbiAgICBwcmljZTogJyQxNDknLFxuICAgIHByaWNlSWQ6IFNUUklQRV9QUklDRV9JRFMuRU5URVJQUklTRSxcbiAgICBwcm9kdWN0SWQ6IFNUUklQRV9QUk9EVUNUX0lEUy5FTlRFUlBSSVNFLFxuICAgIGZlYXR1cmVzOiBbXG4gICAgICAnVW5saW1pdGVkIEFQSSByZXF1ZXN0cycsXG4gICAgICAnVW5saW1pdGVkIGNvbmZpZ3VyYXRpb25zJyxcbiAgICAgICdVbmxpbWl0ZWQgQVBJIGtleXMnLFxuICAgICAgJ0FsbCAzMDArIG1vZGVscyArIHByaW9yaXR5IGFjY2VzcycsXG4gICAgICAnQWxsIHJvdXRpbmcgc3RyYXRlZ2llcycsXG4gICAgICAnVW5saW1pdGVkIGN1c3RvbSByb2xlcycsXG4gICAgICAnQWxsIGZlYXR1cmVzICsgcHJpb3JpdHkgc3VwcG9ydCcsXG4gICAgICAnVW5saW1pdGVkIGtub3dsZWRnZSBiYXNlIGRvY3VtZW50cycsXG4gICAgICAnQWR2YW5jZWQgc2VtYW50aWMgY2FjaGluZycsXG4gICAgICAnQ3VzdG9tIGludGVncmF0aW9ucycsXG4gICAgICAnRGVkaWNhdGVkIHN1cHBvcnQgKyBwaG9uZScsXG4gICAgICAnU0xBIGd1YXJhbnRlZScsXG4gICAgXSxcbiAgICBsaW1pdHM6IHtcbiAgICAgIGNvbmZpZ3VyYXRpb25zOiA5OTk5OTksXG4gICAgICBhcGlLZXlzUGVyQ29uZmlnOiA5OTk5OTksXG4gICAgICBhcGlSZXF1ZXN0czogOTk5OTk5LFxuICAgICAgY2FuVXNlQWR2YW5jZWRSb3V0aW5nOiB0cnVlLFxuICAgICAgY2FuVXNlQ3VzdG9tUm9sZXM6IHRydWUsXG4gICAgICBtYXhDdXN0b21Sb2xlczogOTk5OTk5LFxuICAgICAgY2FuVXNlUHJvbXB0RW5naW5lZXJpbmc6IHRydWUsXG4gICAgICBjYW5Vc2VLbm93bGVkZ2VCYXNlOiB0cnVlLFxuICAgICAga25vd2xlZGdlQmFzZURvY3VtZW50czogOTk5OTk5LFxuICAgICAgY2FuVXNlU2VtYW50aWNDYWNoaW5nOiB0cnVlLFxuICAgIH0sXG4gIH0sXG59O1xuXG5leHBvcnQgZnVuY3Rpb24gZ2V0VGllckNvbmZpZyh0aWVyOiBTdWJzY3JpcHRpb25UaWVyKTogVGllckNvbmZpZyB7XG4gIHJldHVybiBUSUVSX0NPTkZJR1NbdGllcl07XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBnZXRQcmljZUlkRm9yVGllcih0aWVyOiBTdWJzY3JpcHRpb25UaWVyKTogc3RyaW5nIHwgbnVsbCB7XG4gIHJldHVybiBUSUVSX0NPTkZJR1NbdGllcl0ucHJpY2VJZDtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGdldFRpZXJGcm9tUHJpY2VJZChwcmljZUlkOiBzdHJpbmcpOiBTdWJzY3JpcHRpb25UaWVyIHtcbiAgZm9yIChjb25zdCBbdGllciwgY29uZmlnXSBvZiBPYmplY3QuZW50cmllcyhUSUVSX0NPTkZJR1MpKSB7XG4gICAgaWYgKGNvbmZpZy5wcmljZUlkID09PSBwcmljZUlkKSB7XG4gICAgICByZXR1cm4gdGllciBhcyBTdWJzY3JpcHRpb25UaWVyO1xuICAgIH1cbiAgfVxuICByZXR1cm4gJ2ZyZWUnOyAvLyBEZWZhdWx0IGZhbGxiYWNrIHRvIGZyZWUgdGllclxufVxuXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0UHJpY2UodGllcjogU3Vic2NyaXB0aW9uVGllcik6IHN0cmluZyB7XG4gIHJldHVybiBUSUVSX0NPTkZJR1NbdGllcl0ucHJpY2U7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBjYW5QZXJmb3JtQWN0aW9uKFxuICB0aWVyOiBTdWJzY3JpcHRpb25UaWVyLFxuICBhY3Rpb246ICdjcmVhdGVfY29uZmlnJyB8ICdjcmVhdGVfYXBpX2tleScsXG4gIGN1cnJlbnRDb3VudDogbnVtYmVyXG4pOiBib29sZWFuIHtcbiAgY29uc3QgbGltaXRzID0gVElFUl9DT05GSUdTW3RpZXJdLmxpbWl0cztcblxuICBzd2l0Y2ggKGFjdGlvbikge1xuICAgIGNhc2UgJ2NyZWF0ZV9jb25maWcnOlxuICAgICAgcmV0dXJuIGN1cnJlbnRDb3VudCA8IGxpbWl0cy5jb25maWd1cmF0aW9ucztcbiAgICBjYXNlICdjcmVhdGVfYXBpX2tleSc6XG4gICAgICByZXR1cm4gY3VycmVudENvdW50IDwgbGltaXRzLmFwaUtleXNQZXJDb25maWc7XG4gICAgZGVmYXVsdDpcbiAgICAgIHJldHVybiB0cnVlO1xuICB9XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBoYXNGZWF0dXJlQWNjZXNzKFxuICB0aWVyOiBTdWJzY3JpcHRpb25UaWVyLFxuICBmZWF0dXJlOiAnY3VzdG9tX3JvbGVzJyB8ICdrbm93bGVkZ2VfYmFzZScgfCAnYWR2YW5jZWRfcm91dGluZycgfCAncHJvbXB0X2VuZ2luZWVyaW5nJyB8ICdzZW1hbnRpY19jYWNoaW5nJyB8ICdjb25maWd1cmF0aW9ucydcbik6IGJvb2xlYW4ge1xuICBjb25zdCBsaW1pdHMgPSBUSUVSX0NPTkZJR1NbdGllcl0ubGltaXRzO1xuXG4gIHN3aXRjaCAoZmVhdHVyZSkge1xuICAgIGNhc2UgJ2N1c3RvbV9yb2xlcyc6XG4gICAgICByZXR1cm4gbGltaXRzLmNhblVzZUN1c3RvbVJvbGVzO1xuICAgIGNhc2UgJ2tub3dsZWRnZV9iYXNlJzpcbiAgICAgIHJldHVybiBsaW1pdHMuY2FuVXNlS25vd2xlZGdlQmFzZTtcbiAgICBjYXNlICdhZHZhbmNlZF9yb3V0aW5nJzpcbiAgICAgIHJldHVybiBsaW1pdHMuY2FuVXNlQWR2YW5jZWRSb3V0aW5nO1xuICAgIGNhc2UgJ3Byb21wdF9lbmdpbmVlcmluZyc6XG4gICAgICByZXR1cm4gbGltaXRzLmNhblVzZVByb21wdEVuZ2luZWVyaW5nO1xuICAgIGNhc2UgJ3NlbWFudGljX2NhY2hpbmcnOlxuICAgICAgcmV0dXJuIGxpbWl0cy5jYW5Vc2VTZW1hbnRpY0NhY2hpbmc7XG4gICAgY2FzZSAnY29uZmlndXJhdGlvbnMnOlxuICAgICAgcmV0dXJuIGxpbWl0cy5jb25maWd1cmF0aW9ucyA+IDA7IC8vIEFsbCB0aWVycyBjYW4gY3JlYXRlIGF0IGxlYXN0IHNvbWUgY29uZmlndXJhdGlvbnNcbiAgICBkZWZhdWx0OlxuICAgICAgcmV0dXJuIGZhbHNlO1xuICB9XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgU3Vic2NyaXB0aW9uU3RhdHVzIHtcbiAgaGFzQWN0aXZlU3Vic2NyaXB0aW9uOiBib29sZWFuO1xuICB0aWVyOiBTdWJzY3JpcHRpb25UaWVyO1xuICBzdGF0dXM6IHN0cmluZyB8IG51bGw7XG4gIGN1cnJlbnRQZXJpb2RFbmQ6IHN0cmluZyB8IG51bGw7XG4gIGNhbmNlbEF0UGVyaW9kRW5kOiBib29sZWFuO1xuICBpc0ZyZWU6IGJvb2xlYW47XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgVXNhZ2VTdGF0dXMge1xuICB0aWVyOiBTdWJzY3JpcHRpb25UaWVyO1xuICB1c2FnZToge1xuICAgIGNvbmZpZ3VyYXRpb25zOiBudW1iZXI7XG4gICAgYXBpS2V5czogbnVtYmVyO1xuICAgIGFwaVJlcXVlc3RzOiBudW1iZXI7XG4gIH07XG4gIGxpbWl0czoge1xuICAgIGNvbmZpZ3VyYXRpb25zOiBudW1iZXI7XG4gICAgYXBpS2V5c1BlckNvbmZpZzogbnVtYmVyO1xuICAgIGFwaVJlcXVlc3RzOiBudW1iZXI7XG4gICAgY2FuVXNlQWR2YW5jZWRSb3V0aW5nOiBib29sZWFuO1xuICAgIGNhblVzZUN1c3RvbVJvbGVzOiBib29sZWFuO1xuICAgIG1heEN1c3RvbVJvbGVzOiBudW1iZXI7XG4gICAgY2FuVXNlUHJvbXB0RW5naW5lZXJpbmc6IGJvb2xlYW47XG4gICAgY2FuVXNlS25vd2xlZGdlQmFzZTogYm9vbGVhbjtcbiAgICBrbm93bGVkZ2VCYXNlRG9jdW1lbnRzOiBudW1iZXI7XG4gICAgY2FuVXNlU2VtYW50aWNDYWNoaW5nOiBib29sZWFuO1xuICB9O1xuICBjYW5DcmVhdGVDb25maWc6IGJvb2xlYW47XG4gIGNhbkNyZWF0ZUFwaUtleTogYm9vbGVhbjtcbn1cbiJdLCJuYW1lcyI6WyJTVFJJUEVfUFJJQ0VfSURTIiwiU1RSSVBFX1BST0RVQ1RfSURTIiwiVElFUl9DT05GSUdTIiwiZnJlZSIsIm5hbWUiLCJwcmljZSIsInByaWNlSWQiLCJGUkVFIiwicHJvZHVjdElkIiwiZmVhdHVyZXMiLCJsaW1pdHMiLCJjb25maWd1cmF0aW9ucyIsImFwaUtleXNQZXJDb25maWciLCJhcGlSZXF1ZXN0cyIsImNhblVzZUFkdmFuY2VkUm91dGluZyIsImNhblVzZUN1c3RvbVJvbGVzIiwibWF4Q3VzdG9tUm9sZXMiLCJjYW5Vc2VQcm9tcHRFbmdpbmVlcmluZyIsImNhblVzZUtub3dsZWRnZUJhc2UiLCJrbm93bGVkZ2VCYXNlRG9jdW1lbnRzIiwiY2FuVXNlU2VtYW50aWNDYWNoaW5nIiwic3RhcnRlciIsIlNUQVJURVIiLCJwcm9mZXNzaW9uYWwiLCJQUk9GRVNTSU9OQUwiLCJlbnRlcnByaXNlIiwiRU5URVJQUklTRSIsImdldFRpZXJDb25maWciLCJ0aWVyIiwiZ2V0UHJpY2VJZEZvclRpZXIiLCJnZXRUaWVyRnJvbVByaWNlSWQiLCJjb25maWciLCJPYmplY3QiLCJlbnRyaWVzIiwiZm9ybWF0UHJpY2UiLCJjYW5QZXJmb3JtQWN0aW9uIiwiYWN0aW9uIiwiY3VycmVudENvdW50IiwiaGFzRmVhdHVyZUFjY2VzcyIsImZlYXR1cmUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/stripe-client.ts\n"));

/***/ })

});