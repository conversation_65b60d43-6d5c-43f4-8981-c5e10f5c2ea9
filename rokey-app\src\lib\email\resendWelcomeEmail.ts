import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

interface WelcomeEmailData {
  userEmail: string;
  userName: string;
  userTier: string;
}

/**
 * Send welcome email using Resend (server-side optimized)
 */
export async function sendResendWelcomeEmail(data: WelcomeEmailData): Promise<boolean> {
  try {
    const { userEmail, userName, userTier } = data;
    
    // Email content
    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to RouKey</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif; 
            line-height: 1.6; 
            color: #333; 
            margin: 0; 
            padding: 0; 
            background-color: #f8f9fa;
        }
        .container { 
            max-width: 600px; 
            margin: 0 auto; 
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header { 
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%); 
            color: white; 
            padding: 40px 30px; 
            text-align: center; 
        }
        .header h1 { 
            margin: 0; 
            font-size: 28px; 
            font-weight: 700; 
        }
        .header p { 
            margin: 10px 0 0 0; 
            font-size: 16px; 
            opacity: 0.9; 
        }
        .content { 
            padding: 40px 30px; 
        }
        .highlight { 
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); 
            padding: 20px; 
            border-radius: 8px; 
            margin: 20px 0; 
            border-left: 4px solid #ff6b35;
        }
        .feature { 
            background: #f8f9fa; 
            padding: 20px; 
            margin: 15px 0; 
            border-radius: 8px; 
            border-left: 4px solid #ff6b35; 
        }
        .feature strong { 
            color: #ff6b35; 
            display: block; 
            margin-bottom: 8px; 
        }
        .button { 
            display: inline-block; 
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%); 
            color: white; 
            padding: 14px 28px; 
            text-decoration: none; 
            border-radius: 8px; 
            font-weight: 600; 
            margin: 10px 10px 10px 0; 
            transition: transform 0.2s;
        }
        .button:hover { 
            transform: translateY(-2px); 
        }
        .button-secondary { 
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%); 
        }
        .footer { 
            background: #f8f9fa; 
            padding: 30px; 
            text-align: center; 
            font-size: 14px; 
            color: #666; 
            border-top: 1px solid #e9ecef;
        }
        .footer strong { 
            color: #ff6b35; 
        }
        ul, ol { 
            padding-left: 20px; 
        }
        li { 
            margin-bottom: 8px; 
        }
        .text-center { 
            text-align: center; 
        }
        .tier-badge {
            display: inline-block;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Welcome to RouKey! 🚀</h1>
            <p>Your AI Gateway is Ready</p>
        </div>
        
        <div class="content">
            <p>Hi <strong>${userName}</strong>,</p>
            
            <p>Welcome to RouKey! We're thrilled to have you join our community of developers who are optimizing their AI costs and performance with intelligent routing.</p>
            
            <div class="highlight">
                <p><strong>🎉 Your <span class="tier-badge">${userTier}</span> account is now active and ready to use!</strong></p>
            </div>
            
            <h2 style="color: #ff6b35; margin-top: 30px;">🚀 Quick Start Guide</h2>
            
            <div class="feature">
                <strong>1. Access Your Dashboard</strong>
                Start configuring your AI routing strategies and monitoring performance.
            </div>
            
            <div class="feature">
                <strong>2. Add Your API Keys</strong>
                Configure your OpenAI, Anthropic, Google, and other provider keys for intelligent routing.
            </div>
            
            <div class="feature">
                <strong>3. Set Up Routing</strong>
                Choose from our intelligent routing strategies to optimize costs and performance.
            </div>
            
            <div class="feature">
                <strong>4. Start Saving</strong>
                Begin optimizing your AI costs immediately with smart model selection.
            </div>
            
            <h2 style="color: #ff6b35; margin-top: 30px;">✅ What You Can Do Next</h2>
            <ul>
                <li><strong>Explore Intelligent Routing:</strong> Let RouKey automatically route requests to the optimal model</li>
                <li><strong>Configure Multiple Providers:</strong> Add keys from 300+ AI models</li>
                <li><strong>Monitor Performance:</strong> Track costs, latency, and success rates in real-time</li>
                <li><strong>Read Our Documentation:</strong> Learn advanced routing strategies</li>
            </ul>
            
            <h2 style="color: #ff6b35; margin-top: 30px;">💡 Pro Tips for Getting Started</h2>
            <ol>
                <li><strong>Start with Fallback Routing:</strong> Configure a simple fallback strategy first</li>
                <li><strong>Add Multiple Keys:</strong> Set up keys from different providers for better reliability</li>
                <li><strong>Monitor Analytics:</strong> Check your dashboard regularly to see cost savings</li>
            </ol>
            
            <div class="text-center" style="margin: 40px 0;">
                <a href="https://roukey.online/dashboard" class="button">Get Started Now</a>
                <a href="https://roukey.online/docs" class="button button-secondary">View Documentation</a>
            </div>
            
            <p>Thank you for choosing RouKey. We're here to help you optimize your AI operations!</p>
            
            <p>Best regards,<br>
            <strong>The RouKey Team</strong></p>
        </div>
        
        <div class="footer">
            <p><strong>RouKey - Smart AI Gateway</strong></p>
            <p>© ${new Date().getFullYear()} DRIM LLC. All rights reserved.</p>
            <p>Need help? Reply to this email or contact us at <a href="mailto:<EMAIL>" style="color: #ff6b35;"><EMAIL></a></p>
            <p style="margin-top: 20px; font-size: 12px; color: #999;">
                You received this email because you signed up for RouKey. 
                If you have any questions, please contact our support team.
            </p>
        </div>
    </div>
</body>
</html>`;

    // Send email using Resend
    const { data: emailData, error } = await resend.emails.send({
      from: 'RouKey <<EMAIL>>', // Use your verified domain
      to: [userEmail],
      subject: 'Welcome to RouKey - Your AI Gateway is Ready! 🚀',
      html: htmlContent,
      text: `Hi ${userName},

Welcome to RouKey! Your ${userTier} account is now active.

Quick Start:
1. Access Your Dashboard: https://roukey.online/dashboard
2. Add Your API Keys
3. Set Up Routing
4. Start Saving

Need help? Contact <NAME_EMAIL>

Best regards,
The RouKey Team

© ${new Date().getFullYear()} DRIM LLC. All rights reserved.`,
    });

    if (error) {
      console.error('❌ Resend error:', error);
      return false;
    }

    console.log('✅ Welcome email sent successfully via Resend to:', userEmail);
    console.log('📧 Email ID:', emailData?.id);
    return true;

  } catch (error) {
    console.error('❌ Failed to send welcome email via Resend:', error);
    return false;
  }
}
