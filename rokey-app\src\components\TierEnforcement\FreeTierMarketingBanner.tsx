'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { SparklesIcon, ArrowUpIcon } from '@heroicons/react/24/outline';
import { useSubscription } from '@/hooks/useSubscription';
import { useRouter } from 'next/navigation';

interface FreeTierMarketingBannerProps {
  message?: string;
  variant?: 'compact' | 'full';
  className?: string;
}

/**
 * ChatGPT-style marketing banner for free tier users
 * Only shows for free tier users to encourage upgrade
 */
export function FreeTierMarketingBanner({
  message = "Unlock intelligent routing with Plus",
  variant = 'compact',
  className = ''
}: FreeTierMarketingBannerProps) {
  const { subscriptionStatus, createCheckoutSession } = useSubscription();
  const router = useRouter();

  // Only show for free tier users
  if (!subscriptionStatus || subscriptionStatus.tier !== 'free') {
    return null;
  }

  const handleUpgrade = async () => {
    try {
      await createCheckoutSession('starter');
    } catch (error) {
      console.error('Error creating checkout session:', error);
      router.push('/pricing');
    }
  };

  if (variant === 'compact') {
    return (
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className={`bg-gradient-to-r from-orange-500/10 to-orange-600/10 border border-orange-500/20 rounded-lg p-3 ${className}`}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-1.5 bg-orange-500/20 rounded-lg">
              <SparklesIcon className="h-4 w-4 text-orange-400" />
            </div>
            <div>
              <p className="text-sm font-medium text-white">{message}</p>
              <p className="text-xs text-gray-400">RouKey Plus gives you smarter routing and unlimited configurations.</p>
            </div>
          </div>
          <button
            onClick={handleUpgrade}
            className="inline-flex items-center px-3 py-1.5 bg-orange-600 text-white text-sm font-medium rounded-lg hover:bg-orange-700 transition-colors duration-200 whitespace-nowrap"
          >
            Get Plus
          </button>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`bg-gradient-to-r from-orange-500/10 to-orange-600/10 border border-orange-500/20 rounded-xl p-6 ${className}`}
    >
      <div className="flex items-start space-x-4">
        <div className="p-2 bg-orange-500/20 rounded-xl">
          <SparklesIcon className="h-6 w-6 text-orange-400" />
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-white mb-2">{message}</h3>
          <p className="text-gray-300 mb-4">
            Upgrade to RouKey Plus for intelligent routing, unlimited configurations, 
            and advanced features that optimize your AI costs and performance.
          </p>
          <div className="flex items-center space-x-4">
            <button
              onClick={handleUpgrade}
              className="inline-flex items-center px-4 py-2 bg-orange-600 text-white text-sm font-medium rounded-lg hover:bg-orange-700 transition-colors duration-200"
            >
              <ArrowUpIcon className="w-4 h-4 mr-2" />
              Upgrade to Plus
            </button>
            <button
              onClick={() => router.push('/pricing')}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-orange-400 hover:text-orange-300 transition-colors duration-200"
            >
              View All Plans
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  );
}

/**
 * Subtle marketing message for starter tier users
 */
export function StarterTierHint({ className = '' }: { className?: string }) {
  const { subscriptionStatus } = useSubscription();
  const router = useRouter();

  // Only show for starter tier users
  if (!subscriptionStatus || subscriptionStatus.tier !== 'starter') {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className={`text-center py-2 ${className}`}
    >
      <p className="text-xs text-gray-500">
        Need unlimited configurations?{' '}
        <button
          onClick={() => router.push('/billing')}
          className="text-orange-400 hover:text-orange-300 underline transition-colors"
        >
          Upgrade to Professional
        </button>
      </p>
    </motion.div>
  );
}
