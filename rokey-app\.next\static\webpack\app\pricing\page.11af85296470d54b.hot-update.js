"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pricing/page",{

/***/ "(app-pages-browser)/./src/app/pricing/page.tsx":
/*!**********************************!*\
  !*** ./src/app/pricing/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PricingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_BuildingOfficeIcon_CheckIcon_CpuChipIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,BuildingOfficeIcon,CheckIcon,CpuChipIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_BuildingOfficeIcon_CheckIcon_CpuChipIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,BuildingOfficeIcon,CheckIcon,CpuChipIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_BuildingOfficeIcon_CheckIcon_CpuChipIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,BuildingOfficeIcon,CheckIcon,CpuChipIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_BuildingOfficeIcon_CheckIcon_CpuChipIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,BuildingOfficeIcon,CheckIcon,CpuChipIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BuildingOfficeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_BuildingOfficeIcon_CheckIcon_CpuChipIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,BuildingOfficeIcon,CheckIcon,CpuChipIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_BuildingOfficeIcon_CheckIcon_CpuChipIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,BuildingOfficeIcon,CheckIcon,CpuChipIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _components_landing_Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\");\n/* harmony import */ var _components_landing_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/landing/EnhancedGridBackground */ \"(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst pricingTiers = [\n    {\n        name: \"Free\",\n        price: 0,\n        description: \"Perfect for getting started and testing RouKey\",\n        features: [\n            \"Unlimited API requests\",\n            \"1 Custom Configuration\",\n            \"3 API Keys per config\",\n            \"All 300+ AI models\",\n            \"Strict fallback routing only\",\n            \"Basic analytics only\",\n            \"No custom roles, basic router only\",\n            \"Limited logs\",\n            \"Community support\"\n        ],\n        notIncluded: [\n            \"Advanced routing strategies\",\n            \"Custom roles\",\n            \"Prompt engineering\",\n            \"Knowledge base\",\n            \"Semantic caching\"\n        ],\n        cta: \"Start free trial\",\n        popular: false,\n        icon: _barrel_optimize_names_BoltIcon_BuildingOfficeIcon_CheckIcon_CpuChipIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        gradient: \"from-blue-500 to-purple-600\"\n    },\n    {\n        name: \"Starter\",\n        price: 15,\n        description: \"Perfect for individual developers and small projects\",\n        features: [\n            \"Unlimited API requests\",\n            \"15 Custom Configurations\",\n            \"5 API Keys per config\",\n            \"All 300+ AI models\",\n            \"Intelligent routing strategies\",\n            \"Up to 3 custom roles\",\n            \"Intelligent role routing\",\n            \"Prompt engineering (no file upload)\",\n            \"Enhanced logs and analytics\",\n            \"Community support\"\n        ],\n        notIncluded: [\n            \"Knowledge base\",\n            \"Semantic caching\",\n            \"Priority support\"\n        ],\n        cta: \"Get Started\",\n        popular: false,\n        icon: _barrel_optimize_names_BoltIcon_BuildingOfficeIcon_CheckIcon_CpuChipIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        gradient: \"from-orange-500 to-orange-600\"\n    },\n    {\n        name: \"Professional\",\n        price: 50,\n        description: \"Ideal for growing businesses and development teams\",\n        features: [\n            \"Unlimited API requests\",\n            \"Unlimited Custom Configurations\",\n            \"Unlimited API Keys per config\",\n            \"All 300+ AI models\",\n            \"All advanced routing strategies\",\n            \"Unlimited custom roles\",\n            \"Prompt engineering + Knowledge base (5 documents)\",\n            \"Semantic caching\",\n            \"Advanced analytics and logging\",\n            \"Priority email support\"\n        ],\n        notIncluded: [\n            \"Custom integrations\",\n            \"Phone support\",\n            \"SLA guarantee\"\n        ],\n        cta: \"Get Started\",\n        popular: true,\n        icon: _barrel_optimize_names_BoltIcon_BuildingOfficeIcon_CheckIcon_CpuChipIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        gradient: \"from-purple-500 to-purple-600\"\n    },\n    {\n        name: \"Enterprise\",\n        price: 170,\n        description: \"For large organizations with advanced AI routing needs\",\n        features: [\n            \"Unlimited API requests\",\n            \"Unlimited configurations\",\n            \"Unlimited API keys\",\n            \"All 300+ models + priority access\",\n            \"All routing strategies\",\n            \"Unlimited custom roles\",\n            \"All features + priority support\",\n            \"Unlimited knowledge base documents\",\n            \"Advanced semantic caching\",\n            \"Custom integrations\",\n            \"Dedicated support + phone\",\n            \"SLA guarantee\"\n        ],\n        notIncluded: [],\n        cta: \"Get Started\",\n        popular: false,\n        icon: _barrel_optimize_names_BoltIcon_BuildingOfficeIcon_CheckIcon_CpuChipIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        gradient: \"from-emerald-500 to-emerald-600\"\n    }\n];\nconst comparisonFeatures = [\n    {\n        category: \"Core features\",\n        features: [\n            {\n                name: \"Leading-edge UI\",\n                free: \"✓\",\n                starter: \"✓\",\n                pro: \"✓\"\n            },\n            {\n                name: \"All integrations\",\n                free: \"✓\",\n                starter: \"✓\",\n                pro: \"✓\"\n            },\n            {\n                name: \"Streaming operations\",\n                free: \"✓\",\n                starter: \"✓\",\n                pro: \"✓\"\n            },\n            {\n                name: \"Analytics & Monitoring\",\n                free: \"Basic\",\n                starter: \"Enhanced\",\n                pro: \"Advanced\"\n            }\n        ]\n    },\n    {\n        category: \"Developer tools\",\n        features: [\n            {\n                name: \"API Requests per month\",\n                free: \"Unlimited\",\n                starter: \"Unlimited\",\n                pro: \"Unlimited\"\n            },\n            {\n                name: \"Custom Configurations\",\n                free: \"1\",\n                starter: \"15\",\n                pro: \"Unlimited\"\n            },\n            {\n                name: \"API Keys per config\",\n                free: \"3\",\n                starter: \"5\",\n                pro: \"Unlimited\"\n            },\n            {\n                name: \"Supported AI Models\",\n                free: \"300+\",\n                starter: \"300+\",\n                pro: \"300+\"\n            },\n            {\n                name: \"Routing Strategies\",\n                free: \"Strict fallback only\",\n                starter: \"Intelligent routing\",\n                pro: \"All strategies\"\n            },\n            {\n                name: \"Custom Roles\",\n                free: \"✗\",\n                starter: \"Up to 3\",\n                pro: \"Unlimited\"\n            },\n            {\n                name: \"Intelligent Role Routing\",\n                free: \"✗\",\n                starter: \"✓\",\n                pro: \"✓\"\n            }\n        ]\n    },\n    {\n        category: \"Advanced features\",\n        features: [\n            {\n                name: \"Prompt Engineering\",\n                free: \"✗\",\n                starter: \"✓ (no upload)\",\n                pro: \"✓ + Knowledge base\"\n            },\n            {\n                name: \"Knowledge Base Documents\",\n                free: \"✗\",\n                starter: \"✗\",\n                pro: \"5 documents\"\n            },\n            {\n                name: \"Semantic Caching\",\n                free: \"✗\",\n                starter: \"✗\",\n                pro: \"✓\"\n            },\n            {\n                name: \"Support Level\",\n                free: \"Community\",\n                starter: \"Community\",\n                pro: \"Priority Email\"\n            }\n        ]\n    }\n];\n// Helper function to render feature values with proper styling\nconst renderFeatureValue = (value)=>{\n    if (value === \"✓\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_BuildingOfficeIcon_CheckIcon_CpuChipIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            className: \"h-5 w-5 text-green-400 mx-auto\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n            lineNumber: 154,\n            columnNumber: 12\n        }, undefined);\n    }\n    if (value === \"✗\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_BuildingOfficeIcon_CheckIcon_CpuChipIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            className: \"h-5 w-5 text-red-400 mx-auto\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n            lineNumber: 157,\n            columnNumber: 12\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"text-gray-300\",\n        children: value\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n        lineNumber: 159,\n        columnNumber: 10\n    }, undefined);\n};\nfunction PricingPageContent() {\n    _s();\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const enterpriseCardRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleMouseMove = (e)=>{\n        if (enterpriseCardRef.current) {\n            const rect = enterpriseCardRef.current.getBoundingClientRect();\n            setMousePosition({\n                x: e.clientX - rect.left,\n                y: e.clientY - rect.top\n            });\n        }\n    };\n    // Check for subscription required message\n    const urlParams =  true ? new URLSearchParams(window.location.search) : 0;\n    const message = urlParams === null || urlParams === void 0 ? void 0 : urlParams.get('message');\n    const plan = urlParams === null || urlParams === void 0 ? void 0 : urlParams.get('plan');\n    const showSubscriptionRequired = message === 'subscription_required' || message === 'subscription_check_failed';\n    const showCompletePayment = message === 'complete_payment';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        style: {\n            background: \"radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            showSubscriptionRequired && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-[#ff6b35] text-white py-3 px-4 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm font-medium\",\n                    children: \"\\uD83D\\uDD12 Active subscription required to access the dashboard. Please choose a plan below to continue.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                lineNumber: 191,\n                columnNumber: 9\n            }, this),\n            showCompletePayment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-amber-500 text-white py-3 px-4 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm font-medium\",\n                    children: [\n                        \"⚠️ Please complete your payment to activate your \",\n                        plan ? plan.charAt(0).toUpperCase() + plan.slice(1) : '',\n                        \" plan. Your account is currently pending payment.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                lineNumber: 200,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-20 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        gridSize: 50,\n                        opacity: 0.03,\n                        color: \"#ff6b35\",\n                        variant: \"premium\",\n                        animated: true,\n                        className: \"fixed inset-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.h1, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    className: \"text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6\",\n                                    children: [\n                                        \"Universal AI access with\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600\",\n                                            children: \"your own keys\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.p, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.05\n                                    },\n                                    className: \"text-xl text-gray-300 max-w-3xl mx-auto mb-8\",\n                                    children: \"Choose the plan that fits your needs. All plans include intelligent routing to 300+ AI models with complete cost transparency.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.1\n                                    },\n                                    className: \"flex items-center justify-center gap-6 mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 bg-white rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-sm\",\n                                                    children: \"Monthly\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 opacity-60\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 bg-white/30 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white/60 text-sm\",\n                                                    children: \"Annually\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"pb-20 relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto\",\n                                children: pricingTiers.slice(0, 3).map((tier, index)=>{\n                                    const IconComponent = tier.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: index * 0.1\n                                        },\n                                        className: \"relative rounded-3xl p-8 backdrop-blur-sm border \".concat(tier.popular ? 'bg-white/10 border-orange-500/50 shadow-2xl shadow-orange-500/20' : 'bg-white/5 border-white/10 hover:bg-white/10', \" transition-all duration-300 hover:transform hover:scale-105\"),\n                                        children: [\n                                            tier.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-4 left-1/2 transform -translate-x-1/2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-orange-500 to-orange-600 text-white px-4 py-1 rounded-full text-sm font-medium flex items-center shadow-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_BuildingOfficeIcon_CheckIcon_CpuChipIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        \"Pro\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"inline-flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-r \".concat(tier.gradient, \" mb-4\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                            className: \"h-6 w-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-white mb-2\",\n                                                        children: tier.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 mb-6 text-sm\",\n                                                        children: tier.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-baseline justify-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-4xl font-bold text-white\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    tier.price\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400 ml-2\",\n                                                                children: \"per month, billed monthly\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4 mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-white mb-1\",\n                                                                children: tier.name === 'Free' ? 'Unlimited' : tier.name === 'Starter' ? 'Unlimited' : 'Unlimited'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-400 text-sm\",\n                                                                children: tier.name === 'Free' ? 'API requests to 300+ models' : tier.name === 'Starter' ? 'API requests to 300+ models' : 'API requests to 300+ models'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-400 text-xs mt-1\",\n                                                                children: tier.name === 'Free' ? 'with your own API keys' : tier.name === 'Starter' ? 'with intelligent routing' : 'with advanced orchestration'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400\",\n                                                                        children: \"Configurations:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                        lineNumber: 315,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white\",\n                                                                        children: tier.name === 'Free' ? '1' : tier.name === 'Starter' ? '15' : tier.name === 'Professional' ? 'Unlimited' : 'Unlimited'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                        lineNumber: 316,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400\",\n                                                                        children: \"API Keys per config:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                        lineNumber: 319,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white\",\n                                                                        children: tier.name === 'Free' ? '3' : tier.name === 'Starter' ? '5' : tier.name === 'Professional' ? 'Unlimited' : 'Unlimited'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                        lineNumber: 320,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400\",\n                                                                        children: \"Custom Roles:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                        lineNumber: 323,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white\",\n                                                                        children: tier.name === 'Free' ? 'None' : tier.name === 'Starter' ? 'Up to 3' : 'Unlimited'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                        lineNumber: 324,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: tier.name === 'Free' ? '/auth/signup?plan=free' : \"/auth/signup?plan=\".concat(tier.name.toLowerCase()),\n                                                className: \"block w-full text-center py-3 px-6 rounded-xl font-semibold transition-all duration-300 \".concat(tier.name === 'Enterprise' ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 shadow-lg hover:shadow-xl' : tier.popular ? 'bg-gradient-to-r from-orange-500 to-orange-600 text-white hover:from-orange-600 hover:to-orange-700 shadow-lg hover:shadow-xl' : 'bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 shadow-lg hover:shadow-xl'),\n                                                children: tier.cta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, tier.name, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    className: \"text-center mb-16\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold text-white mb-4\",\n                                        children: \"Looking for something else?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.1\n                                            },\n                                            className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-white mb-3\",\n                                                    children: \"BYOK Framework\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 text-sm mb-4\",\n                                                    children: \"Bring Your Own Keys - maintain complete control over your API costs.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-xs mb-4\",\n                                                    children: \"RouKey never marks up your API costs. You pay providers directly while getting intelligent routing and cost optimization features.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/docs\",\n                                                    className: \"inline-flex items-center text-orange-400 hover:text-orange-300 text-sm font-medium transition-colors\",\n                                                    children: \"Learn more\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.2\n                                            },\n                                            className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-white mb-3\",\n                                                    children: \"Intelligent Routing\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 text-sm mb-4\",\n                                                    children: \"Smart AI model selection based on complexity, cost, and performance requirements.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-xs mb-4\",\n                                                    children: \"Automatically route simple tasks to cost-effective models and complex tasks to premium models for optimal performance and cost savings.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/features\",\n                                                    className: \"inline-flex items-center text-orange-400 hover:text-orange-300 text-sm font-medium transition-colors\",\n                                                    children: \"Learn more\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.3\n                                            },\n                                            className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-white mb-3\",\n                                                    children: \"Smart Cost Optimization\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 text-sm mb-4\",\n                                                    children: \"Save up to 70% on AI costs with intelligent routing strategies.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-xs mb-4\",\n                                                    children: \"Route simple tasks to cheaper models and complex tasks to premium models automatically.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/routing-strategies\",\n                                                    className: \"inline-flex items-center text-orange-400 hover:text-orange-300 text-sm font-medium transition-colors\",\n                                                    children: \"View strategies\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    className: \"text-center mb-16\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold text-white mb-4\",\n                                        children: \"What's included?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"overflow-x-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                    className: \"bg-white/5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-semibold text-white\",\n                                                                children: \"Features\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 459,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-center text-sm font-semibold text-white\",\n                                                                children: \"Starter\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-center text-sm font-semibold text-white bg-orange-500/10\",\n                                                                children: \"Pro\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-center text-sm font-semibold text-white\",\n                                                                children: \"Enterprise\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                    className: \"divide-y divide-white/10\",\n                                                    children: comparisonFeatures.map((category, categoryIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    className: \"bg-white/5\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        colSpan: 4,\n                                                                        className: \"px-6 py-3 text-sm font-semibold text-white\",\n                                                                        children: category.category\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                        lineNumber: 469,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                    lineNumber: 468,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                category.features.map((feature, featureIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        className: \"hover:bg-white/5\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 text-sm text-gray-300\",\n                                                                                children: feature.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                                lineNumber: 475,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 text-sm text-center\",\n                                                                                children: renderFeatureValue(feature.starter)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                                lineNumber: 476,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 text-sm text-center bg-orange-500/5\",\n                                                                                children: renderFeatureValue(feature.pro)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                                lineNumber: 477,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 text-sm text-center\",\n                                                                                children: renderFeatureValue(feature.enterprise)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                                lineNumber: 478,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, \"\".concat(categoryIndex, \"-\").concat(featureIndex), true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                        lineNumber: 474,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            ]\n                                                        }, category.category, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                            lineNumber: 444,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                        lineNumber: 443,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    className: \"text-left mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-6 h-6 rounded bg-orange-500/20 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-orange-400 text-xs font-bold\",\n                                                        children: \"?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-orange-400 text-sm font-medium\",\n                                                    children: \"FAQs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold text-white mb-4\",\n                                            children: \"Frequently asked questions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        {\n                                            question: \"How does RouKey's BYOK model work?\",\n                                            answer: \"With Bring Your Own Keys (BYOK), you provide your own API keys from providers like OpenAI, Anthropic, Google, etc. RouKey intelligently routes your requests to the optimal model while you pay providers directly. No markup, complete cost transparency, and you maintain full control over your API spending.\"\n                                        },\n                                        {\n                                            question: \"What makes RouKey different from other AI gateways?\",\n                                            answer: \"RouKey is the only AI gateway with intelligent multi-agent orchestration, proprietary AI classification for role-based routing, and advanced cost optimization. Unlike simple proxies, RouKey uses its proprietary orchestration engine for complex workflows and can save up to 70% on AI costs through smart routing strategies.\"\n                                        },\n                                        {\n                                            question: \"Can I cancel my subscription at any time?\",\n                                            answer: \"Yes, you can cancel your RouKey subscription at any time through your account settings. Your access will continue until the end of your current billing period, and you won't be charged for the next cycle. Your configurations and API keys remain accessible during the billing period.\"\n                                        },\n                                        {\n                                            question: \"How secure are my API keys with RouKey?\",\n                                            answer: \"Your API keys are encrypted using enterprise-grade AES-256 encryption and stored securely. RouKey follows a BYOK model, meaning you maintain control of your keys. We never store or log your actual AI responses, and all communications are encrypted in transit with SOC 2 compliance.\"\n                                        },\n                                        {\n                                            question: \"What routing strategies does RouKey support?\",\n                                            answer: \"RouKey offers multiple intelligent routing strategies: Intelligent Role Routing (AI-powered classification), Complexity-Based Routing (cost optimization), Strict Fallback (ordered failover), Smart Cost Optimization, and A/B Testing. Each strategy is designed for different use cases and optimization goals.\"\n                                        },\n                                        {\n                                            question: \"Do I need technical knowledge to use RouKey?\",\n                                            answer: \"RouKey is designed for developers but offers different complexity levels. The Free plan provides simple fallback routing that requires minimal setup. Advanced features like multi-agent workflows and custom roles are available for teams that need sophisticated AI orchestration.\"\n                                        },\n                                        {\n                                            question: \"What's included in the multi-agent workflows?\",\n                                            answer: \"RouKey's multi-agent workflows use our proprietary orchestration engine for advanced coordination including sequential workflows, parallel execution, supervisor patterns, and hierarchical coordination. Features include memory persistence, real-time streaming, tool calling, and comprehensive error handling for complex AI tasks.\"\n                                        },\n                                        {\n                                            question: \"How does RouKey handle rate limits and failures?\",\n                                            answer: \"RouKey automatically handles rate limits and API failures through intelligent fallback mechanisms. When one provider hits limits or fails, RouKey seamlessly routes to alternative models. This ensures high availability and reduces the impact of individual provider issues on your applications.\"\n                                        }\n                                    ].map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: index * 0.1\n                                            },\n                                            className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 hover:bg-white/10 transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-white pr-4\",\n                                                            children: faq.question\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-orange-400 text-xl font-light\",\n                                                            children: \"+\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 text-sm mt-4 leading-relaxed\",\n                                                    children: faq.answer\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                        lineNumber: 491,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                lineNumber: 562,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n        lineNumber: 184,\n        columnNumber: 5\n    }, this);\n}\n_s(PricingPageContent, \"6497yMqPGgGxY65KeucnntnPwNY=\");\n_c = PricingPageContent;\nfunction PricingPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            style: {\n                background: \"radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                        lineNumber: 574,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300\",\n                        children: \"Loading pricing...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                        lineNumber: 575,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                lineNumber: 573,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n            lineNumber: 570,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PricingPageContent, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n            lineNumber: 579,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n        lineNumber: 569,\n        columnNumber: 5\n    }, this);\n}\n_c1 = PricingPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"PricingPageContent\");\n$RefreshReg$(_c1, \"PricingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/pricing/page.tsx\n"));

/***/ })

});