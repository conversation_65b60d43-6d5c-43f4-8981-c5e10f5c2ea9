interface WelcomeEmailData {
  userEmail: string;
  userName: string;
  userTier: string;
}

/**
 * Send welcome email to new RouKey users using server-side fetch to EmailJS
 */
export async function sendWelcomeEmail(data: WelcomeEmailData): Promise<boolean> {
  try {
    const templateParams = {
      to_email: data.userEmail,
      to_name: data.userName,
      user_tier: data.userTier,
      company_name: '<PERSON><PERSON><PERSON><PERSON>',
      dashboard_url: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://roukey.online'}/dashboard`,
      docs_url: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://roukey.online'}/docs`,
      support_email: '<EMAIL>',
      current_year: new Date().getFullYear(),
      welcome_date: new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    };

    // Send welcome email using EmailJS REST API
    const response = await fetch('https://api.emailjs.com/api/v1.0/email/send', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        service_id: process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID || 'service_2xtn7iv',
        template_id: process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID || 'template_pg7e1af', // Using existing template for now
        user_id: process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY || '',
        template_params: templateParams
      })
    });

    if (response.ok) {
      console.log('✅ Welcome email sent successfully to:', data.userEmail);
      return true;
    } else {
      console.error('❌ EmailJS API error:', response.status, await response.text());
      return false;
    }
  } catch (error) {
    console.error('❌ Failed to send welcome email:', error);
    return false;
  }
}

/**
 * Welcome email template content for EmailJS
 * This is the template structure you should create in EmailJS dashboard
 */
export const WELCOME_EMAIL_TEMPLATE = `
Subject: Welcome to RouKey - Your AI Gateway is Ready! 🚀

Hi {{to_name}},

Welcome to RouKey! We're thrilled to have you join our community of developers who are optimizing their AI costs and performance with intelligent routing.

🎉 Your {{user_tier}} account is now active and ready to use!

## Quick Start Guide

1. **Access Your Dashboard**: {{dashboard_url}}
2. **Add Your API Keys**: Configure your OpenAI, Anthropic, Google, and other provider keys
3. **Set Up Routing**: Choose from our intelligent routing strategies
4. **Start Saving**: Begin optimizing your AI costs immediately

## What You Can Do Next

✅ **Explore Intelligent Routing**: Let RouKey automatically route requests to the optimal model
✅ **Configure Multiple Providers**: Add keys from 300+ AI models
✅ **Monitor Performance**: Track costs, latency, and success rates
✅ **Read Our Docs**: {{docs_url}}

## Need Help?

- 📚 **Documentation**: {{docs_url}}
- 💬 **Support**: {{support_email}}
- 🌐 **Website**: https://roukey.online

## Pro Tips for Getting Started

1. **Start with Fallback Routing**: Configure a simple fallback strategy first
2. **Add Multiple Keys**: Set up keys from different providers for better reliability
3. **Monitor Analytics**: Check your dashboard regularly to see cost savings

Thank you for choosing RouKey. We're here to help you optimize your AI operations!

Best regards,
The RouKey Team

---
RouKey - Smart AI Gateway
© {{current_year}} DRIM LLC. All rights reserved.

If you have any questions, just reply to this email or contact us at {{support_email}}.
`;
