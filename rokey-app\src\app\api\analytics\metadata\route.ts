import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = createSupabaseServerClientFromRequest(request);
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '30');
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get user's custom API configurations
    const { data: configsData, count: totalConfigs } = await supabase
      .from('custom_api_configs')
      .select('id, name, routing_strategy, routing_strategy_params, browsing_enabled, created_at', { count: 'exact' })
      .eq('user_id', user.id);

    // Get routing strategy breakdown
    const routingStrategies = {};
    if (configsData) {
      configsData.forEach(config => {
        const strategy = config.routing_strategy || 'none';
        routingStrategies[strategy] = (routingStrategies[strategy] || 0) + 1;
      });
    }

    const routingStrategyBreakdown = Object.entries(routingStrategies).map(([strategy, count]) => ({
      strategy: formatStrategyName(strategy),
      count,
      percentage: totalConfigs > 0 ? (count / totalConfigs) * 100 : 0
    }));

    // Get model usage from request logs
    const { data: modelUsageData } = await supabase
      .from('request_logs')
      .select('llm_model_name, llm_provider_name')
      .eq('user_id', user.id)
      .gte('request_timestamp', startDate.toISOString())
      .not('llm_model_name', 'is', null);

    const modelUsage = {};
    const providerUsage = {};
    
    if (modelUsageData) {
      modelUsageData.forEach(log => {
        const model = log.llm_model_name;
        const provider = log.llm_provider_name;
        
        modelUsage[model] = (modelUsage[model] || 0) + 1;
        if (provider) {
          providerUsage[provider] = (providerUsage[provider] || 0) + 1;
        }
      });
    }

    const topModels = Object.entries(modelUsage)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 15)
      .map(([model, count]) => ({ model, count }));

    const topProviders = Object.entries(providerUsage)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([provider, count]) => ({ provider, count }));

    // Get API keys metadata
    const { data: apiKeysData, count: totalApiKeys } = await supabase
      .from('api_keys')
      .select('provider, predefined_model_id, temperature, created_at', { count: 'exact' })
      .eq('user_id', user.id);

    // Temperature distribution (0.0 to 2.0 range)
    const temperatureRanges = {
      'Very Low (0-0.4)': 0,
      'Low (0.4-0.8)': 0,
      'Medium (0.8-1.2)': 0,
      'High (1.2-1.6)': 0,
      'Very High (1.6-2.0)': 0
    };

    if (apiKeysData) {
      apiKeysData.forEach(key => {
        const temp = key.temperature || 0.7;
        if (temp < 0.4) temperatureRanges['Very Low (0-0.4)']++;
        else if (temp < 0.8) temperatureRanges['Low (0.4-0.8)']++;
        else if (temp < 1.2) temperatureRanges['Medium (0.8-1.2)']++;
        else if (temp < 1.6) temperatureRanges['High (1.2-1.6)']++;
        else temperatureRanges['Very High (1.6-2.0)']++;
      });
    }

    const temperatureDistribution = Object.entries(temperatureRanges).map(([range, count]) => ({
      range,
      count,
      percentage: totalApiKeys > 0 ? (count / totalApiKeys) * 100 : 0
    }));

    // Get user-generated API keys metadata
    const { data: userApiKeysData, count: totalUserApiKeys } = await supabase
      .from('user_generated_api_keys')
      .select('permissions, status, total_requests, created_at', { count: 'exact' })
      .eq('user_id', user.id);

    // User API keys status breakdown
    const userApiKeyStatus = {};
    if (userApiKeysData) {
      userApiKeysData.forEach(key => {
        const status = key.status || 'unknown';
        userApiKeyStatus[status] = (userApiKeyStatus[status] || 0) + 1;
      });
    }

    const userApiKeyStatusBreakdown = Object.entries(userApiKeyStatus).map(([status, count]) => ({
      status,
      count,
      percentage: totalUserApiKeys > 0 ? (count / totalUserApiKeys) * 100 : 0
    }));

    // Get request metadata patterns
    const { data: requestMetadata } = await supabase
      .from('request_logs')
      .select('request_payload_summary, response_payload_summary, processing_duration_ms')
      .eq('user_id', user.id)
      .gte('request_timestamp', startDate.toISOString())
      .limit(1000); // Limit for performance

    // Analyze request patterns
    let totalProcessingTime = 0;
    let requestCount = 0;
    const requestSizes = [];
    const responseSizes = [];

    if (requestMetadata) {
      requestMetadata.forEach(log => {
        if (log.processing_duration_ms) {
          totalProcessingTime += log.processing_duration_ms;
          requestCount++;
        }
        
        if (log.request_payload_summary) {
          const requestSize = JSON.stringify(log.request_payload_summary).length;
          requestSizes.push(requestSize);
        }
        
        if (log.response_payload_summary) {
          const responseSize = JSON.stringify(log.response_payload_summary).length;
          responseSizes.push(responseSize);
        }
      });
    }

    const averageProcessingTime = requestCount > 0 ? totalProcessingTime / requestCount : 0;
    const averageRequestSize = requestSizes.length > 0 ? requestSizes.reduce((a, b) => a + b, 0) / requestSizes.length : 0;
    const averageResponseSize = responseSizes.length > 0 ? responseSizes.reduce((a, b) => a + b, 0) / responseSizes.length : 0;

    // Browsing configurations (hidden for now)
    // const browsingEnabledConfigs = configsData ? configsData.filter(config => config.browsing_enabled).length : 0;
    // const browsingDisabledConfigs = (totalConfigs || 0) - browsingEnabledConfigs;

    // Get role usage from quality metrics
    const { data: roleUsageData } = await supabase
      .from('routing_quality_metrics')
      .select('routing_strategy')
      .eq('user_id', user.id)
      .gte('created_at', startDate.toISOString());

    const roleStrategiesUsed = {};
    if (roleUsageData) {
      roleUsageData.forEach(metric => {
        const strategy = metric.routing_strategy || 'unknown';
        roleStrategiesUsed[strategy] = (roleStrategiesUsed[strategy] || 0) + 1;
      });
    }

    const roleStrategyUsage = Object.entries(roleStrategiesUsed).map(([strategy, count]) => ({
      strategy: formatStrategyName(strategy),
      count
    })).sort((a, b) => b.count - a.count);

    // Configuration age analysis
    const configAges = [];
    if (configsData) {
      const now = new Date();
      configsData.forEach(config => {
        const created = new Date(config.created_at);
        const ageInDays = Math.floor((now.getTime() - created.getTime()) / (1000 * 60 * 60 * 24));
        configAges.push(ageInDays);
      });
    }

    const averageConfigAge = configAges.length > 0 ? configAges.reduce((a, b) => a + b, 0) / configAges.length : 0;

    return NextResponse.json({
      success: true,
      data: {
        // Configuration metrics
        totalConfigs: totalConfigs || 0,
        totalApiKeys: totalApiKeys || 0,
        totalUserApiKeys: totalUserApiKeys || 0,
        averageConfigAge,
        
        // Routing strategies
        routingStrategyBreakdown,
        roleStrategyUsage,
        
        // Model and provider usage
        topModels,
        topProviders,
        uniqueModels: Object.keys(modelUsage).length,
        uniqueProviders: Object.keys(providerUsage).length,
        
        // Configuration settings
        temperatureDistribution,
        userApiKeyStatusBreakdown,
        
        // Browsing configuration (hidden for now)
        // browsingConfiguration: {
        //   enabled: browsingEnabledConfigs,
        //   disabled: browsingDisabledConfigs,
        //   enabledPercentage: totalConfigs > 0 ? (browsingEnabledConfigs / totalConfigs) * 100 : 0
        // },
        
        // Performance metrics
        averageProcessingTime,
        averageRequestSize,
        averageResponseSize,
        
        // Period info
        period: `${days} days`,
        startDate: startDate.toISOString(),
        endDate: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error in metadata analytics:', error);
    return NextResponse.json({
      error: 'Failed to fetch metadata analytics',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

function formatStrategyName(strategy: string): string {
  const strategyNames = {
    'none': 'No Routing',
    'intelligent_role': 'Intelligent Role',
    'complexity_round_robin': 'Complexity Round Robin',
    'auto_optimal': 'Auto Optimal',
    'strict_fallback': 'Strict Fallback',
    'cost_optimized': 'Cost Optimized',
    'ab_routing': 'A/B Testing',
    'unknown': 'Unknown'
  };
  
  return strategyNames[strategy] || strategy;
}
