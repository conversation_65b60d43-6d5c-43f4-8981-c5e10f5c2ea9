"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pricing/page",{

/***/ "(app-pages-browser)/./src/app/pricing/page.tsx":
/*!**********************************!*\
  !*** ./src/app/pricing/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PricingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_BuildingOfficeIcon_CheckIcon_CpuChipIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,BuildingOfficeIcon,CheckIcon,CpuChipIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_BuildingOfficeIcon_CheckIcon_CpuChipIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,BuildingOfficeIcon,CheckIcon,CpuChipIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_BuildingOfficeIcon_CheckIcon_CpuChipIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,BuildingOfficeIcon,CheckIcon,CpuChipIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_BuildingOfficeIcon_CheckIcon_CpuChipIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,BuildingOfficeIcon,CheckIcon,CpuChipIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BuildingOfficeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_BuildingOfficeIcon_CheckIcon_CpuChipIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,BuildingOfficeIcon,CheckIcon,CpuChipIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_BuildingOfficeIcon_CheckIcon_CpuChipIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,BuildingOfficeIcon,CheckIcon,CpuChipIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _components_landing_Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\");\n/* harmony import */ var _components_landing_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/landing/EnhancedGridBackground */ \"(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst pricingTiers = [\n    {\n        name: \"Free\",\n        price: 0,\n        description: \"Perfect for getting started and testing RouKey\",\n        features: [\n            \"Unlimited API requests\",\n            \"1 Custom Configuration\",\n            \"3 API Keys per config\",\n            \"All 300+ AI models\",\n            \"Strict fallback routing only\",\n            \"Basic analytics only\",\n            \"No custom roles, basic router only\",\n            \"Limited logs\",\n            \"Community support\"\n        ],\n        notIncluded: [\n            \"Advanced routing strategies\",\n            \"Custom roles\",\n            \"Prompt engineering\",\n            \"Knowledge base\",\n            \"Semantic caching\"\n        ],\n        cta: \"Start free trial\",\n        popular: false,\n        icon: _barrel_optimize_names_BoltIcon_BuildingOfficeIcon_CheckIcon_CpuChipIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        gradient: \"from-blue-500 to-purple-600\"\n    },\n    {\n        name: \"Starter\",\n        price: 15,\n        description: \"Perfect for individual developers and small projects\",\n        features: [\n            \"Unlimited API requests\",\n            \"15 Custom Configurations\",\n            \"5 API Keys per config\",\n            \"All 300+ AI models\",\n            \"Intelligent routing strategies\",\n            \"Up to 3 custom roles\",\n            \"Intelligent role routing\",\n            \"Prompt engineering (no file upload)\",\n            \"Enhanced logs and analytics\",\n            \"Community support\"\n        ],\n        notIncluded: [\n            \"Knowledge base\",\n            \"Semantic caching\",\n            \"Priority support\"\n        ],\n        cta: \"Get Started\",\n        popular: false,\n        icon: _barrel_optimize_names_BoltIcon_BuildingOfficeIcon_CheckIcon_CpuChipIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        gradient: \"from-orange-500 to-orange-600\"\n    },\n    {\n        name: \"Professional\",\n        price: 50,\n        description: \"Ideal for growing businesses and development teams\",\n        features: [\n            \"Unlimited API requests\",\n            \"Unlimited Custom Configurations\",\n            \"Unlimited API Keys per config\",\n            \"All 300+ AI models\",\n            \"All advanced routing strategies\",\n            \"Unlimited custom roles\",\n            \"Prompt engineering + Knowledge base (5 documents)\",\n            \"Semantic caching\",\n            \"Advanced analytics and logging\",\n            \"Priority email support\"\n        ],\n        notIncluded: [\n            \"Custom integrations\",\n            \"Phone support\",\n            \"SLA guarantee\"\n        ],\n        cta: \"Get Started\",\n        popular: true,\n        icon: _barrel_optimize_names_BoltIcon_BuildingOfficeIcon_CheckIcon_CpuChipIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        gradient: \"from-purple-500 to-purple-600\"\n    },\n    {\n        name: \"Enterprise\",\n        price: 170,\n        description: \"For large organizations with advanced AI routing needs\",\n        features: [\n            \"Unlimited API requests\",\n            \"Unlimited configurations\",\n            \"Unlimited API keys\",\n            \"All 300+ models + priority access\",\n            \"All routing strategies\",\n            \"Unlimited custom roles\",\n            \"All features + priority support\",\n            \"Unlimited knowledge base documents\",\n            \"Advanced semantic caching\",\n            \"Custom integrations\",\n            \"Dedicated support + phone\",\n            \"SLA guarantee\"\n        ],\n        notIncluded: [],\n        cta: \"Get Started\",\n        popular: false,\n        icon: _barrel_optimize_names_BoltIcon_BuildingOfficeIcon_CheckIcon_CpuChipIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        gradient: \"from-emerald-500 to-emerald-600\"\n    }\n];\nconst comparisonFeatures = [\n    {\n        category: \"Core features\",\n        features: [\n            {\n                name: \"Leading-edge UI\",\n                free: \"✓\",\n                starter: \"✓\",\n                pro: \"✓\"\n            },\n            {\n                name: \"All integrations\",\n                free: \"✓\",\n                starter: \"✓\",\n                pro: \"✓\"\n            },\n            {\n                name: \"Streaming operations\",\n                free: \"✓\",\n                starter: \"✓\",\n                pro: \"✓\"\n            },\n            {\n                name: \"Analytics & Monitoring\",\n                free: \"Basic\",\n                starter: \"Enhanced\",\n                pro: \"Advanced\"\n            }\n        ]\n    },\n    {\n        category: \"Developer tools\",\n        features: [\n            {\n                name: \"API Requests per month\",\n                free: \"Unlimited\",\n                starter: \"Unlimited\",\n                pro: \"Unlimited\"\n            },\n            {\n                name: \"Custom Configurations\",\n                free: \"1\",\n                starter: \"15\",\n                pro: \"Unlimited\"\n            },\n            {\n                name: \"API Keys per config\",\n                free: \"3\",\n                starter: \"5\",\n                pro: \"Unlimited\"\n            },\n            {\n                name: \"Supported AI Models\",\n                free: \"300+\",\n                starter: \"300+\",\n                pro: \"300+\"\n            },\n            {\n                name: \"Routing Strategies\",\n                free: \"Strict fallback only\",\n                starter: \"Intelligent routing\",\n                pro: \"All strategies\"\n            },\n            {\n                name: \"Custom Roles\",\n                free: \"✗\",\n                starter: \"Up to 3\",\n                pro: \"Unlimited\"\n            },\n            {\n                name: \"Intelligent Role Routing\",\n                free: \"✗\",\n                starter: \"✓\",\n                pro: \"✓\"\n            }\n        ]\n    },\n    {\n        category: \"Advanced features\",\n        features: [\n            {\n                name: \"Prompt Engineering\",\n                free: \"✗\",\n                starter: \"✓ (no upload)\",\n                pro: \"✓ + Knowledge base\"\n            },\n            {\n                name: \"Knowledge Base Documents\",\n                free: \"✗\",\n                starter: \"✗\",\n                pro: \"5 documents\"\n            },\n            {\n                name: \"Semantic Caching\",\n                free: \"✗\",\n                starter: \"✗\",\n                pro: \"✓\"\n            },\n            {\n                name: \"Support Level\",\n                free: \"Community\",\n                starter: \"Community\",\n                pro: \"Priority Email\"\n            }\n        ]\n    }\n];\n// Helper function to render feature values with proper styling\nconst renderFeatureValue = (value)=>{\n    if (value === \"✓\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_BuildingOfficeIcon_CheckIcon_CpuChipIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            className: \"h-5 w-5 text-green-400 mx-auto\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n            lineNumber: 154,\n            columnNumber: 12\n        }, undefined);\n    }\n    if (value === \"✗\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_BuildingOfficeIcon_CheckIcon_CpuChipIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            className: \"h-5 w-5 text-red-400 mx-auto\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n            lineNumber: 157,\n            columnNumber: 12\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"text-gray-300\",\n        children: value\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n        lineNumber: 159,\n        columnNumber: 10\n    }, undefined);\n};\nfunction PricingPageContent() {\n    _s();\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const enterpriseCardRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleMouseMove = (e)=>{\n        if (enterpriseCardRef.current) {\n            const rect = enterpriseCardRef.current.getBoundingClientRect();\n            setMousePosition({\n                x: e.clientX - rect.left,\n                y: e.clientY - rect.top\n            });\n        }\n    };\n    // Check for subscription required message\n    const urlParams =  true ? new URLSearchParams(window.location.search) : 0;\n    const message = urlParams === null || urlParams === void 0 ? void 0 : urlParams.get('message');\n    const plan = urlParams === null || urlParams === void 0 ? void 0 : urlParams.get('plan');\n    const showSubscriptionRequired = message === 'subscription_required' || message === 'subscription_check_failed';\n    const showCompletePayment = message === 'complete_payment';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        style: {\n            background: \"radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            showSubscriptionRequired && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-[#ff6b35] text-white py-3 px-4 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm font-medium\",\n                    children: \"\\uD83D\\uDD12 Active subscription required to access the dashboard. Please choose a plan below to continue.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                lineNumber: 191,\n                columnNumber: 9\n            }, this),\n            showCompletePayment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-amber-500 text-white py-3 px-4 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm font-medium\",\n                    children: [\n                        \"⚠️ Please complete your payment to activate your \",\n                        plan ? plan.charAt(0).toUpperCase() + plan.slice(1) : '',\n                        \" plan. Your account is currently pending payment.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                lineNumber: 200,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-20 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        gridSize: 50,\n                        opacity: 0.03,\n                        color: \"#ff6b35\",\n                        variant: \"premium\",\n                        animated: true,\n                        className: \"fixed inset-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.h1, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    className: \"text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6\",\n                                    children: [\n                                        \"Universal AI access with\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600\",\n                                            children: \"your own keys\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.p, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.05\n                                    },\n                                    className: \"text-xl text-gray-300 max-w-3xl mx-auto mb-8\",\n                                    children: \"Choose the plan that fits your needs. All plans include intelligent routing to 300+ AI models with complete cost transparency.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.1\n                                    },\n                                    className: \"flex items-center justify-center gap-6 mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 bg-white rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-sm\",\n                                                    children: \"Monthly\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 opacity-60\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 bg-white/30 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white/60 text-sm\",\n                                                    children: \"Annually\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"pb-20 relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto\",\n                                children: pricingTiers.slice(0, 3).map((tier, index)=>{\n                                    const IconComponent = tier.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: index * 0.1\n                                        },\n                                        className: \"relative rounded-3xl p-8 backdrop-blur-sm border \".concat(tier.popular ? 'bg-white/10 border-orange-500/50 shadow-2xl shadow-orange-500/20' : 'bg-white/5 border-white/10 hover:bg-white/10', \" transition-all duration-300 hover:transform hover:scale-105\"),\n                                        children: [\n                                            tier.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-4 left-1/2 transform -translate-x-1/2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-orange-500 to-orange-600 text-white px-4 py-1 rounded-full text-sm font-medium flex items-center shadow-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_BuildingOfficeIcon_CheckIcon_CpuChipIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        \"Pro\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"inline-flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-r \".concat(tier.gradient, \" mb-4\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                            className: \"h-6 w-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-white mb-2\",\n                                                        children: tier.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 mb-6 text-sm\",\n                                                        children: tier.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-baseline justify-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-4xl font-bold text-white\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    tier.price\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400 ml-2\",\n                                                                children: \"per month, billed monthly\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4 mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-white mb-1\",\n                                                                children: tier.name === 'Free' ? 'Unlimited' : tier.name === 'Starter' ? 'Unlimited' : 'Unlimited'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-400 text-sm\",\n                                                                children: tier.name === 'Free' ? 'API requests to 300+ models' : tier.name === 'Starter' ? 'API requests to 300+ models' : 'API requests to 300+ models'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-400 text-xs mt-1\",\n                                                                children: tier.name === 'Free' ? 'with your own API keys' : tier.name === 'Starter' ? 'with intelligent routing' : 'with advanced orchestration'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400\",\n                                                                        children: \"Configurations:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                        lineNumber: 315,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white\",\n                                                                        children: tier.name === 'Free' ? '1' : tier.name === 'Starter' ? '15' : tier.name === 'Professional' ? 'Unlimited' : 'Unlimited'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                        lineNumber: 316,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400\",\n                                                                        children: \"API Keys per config:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                        lineNumber: 319,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white\",\n                                                                        children: tier.name === 'Free' ? '3' : tier.name === 'Starter' ? '5' : tier.name === 'Professional' ? 'Unlimited' : 'Unlimited'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                        lineNumber: 320,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400\",\n                                                                        children: \"Custom Roles:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                        lineNumber: 323,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white\",\n                                                                        children: tier.name === 'Free' ? 'None' : tier.name === 'Starter' ? 'Up to 3' : 'Unlimited'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                        lineNumber: 324,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: tier.name === 'Free' ? '/auth/signup?plan=free' : \"/auth/signup?plan=\".concat(tier.name.toLowerCase()),\n                                                className: \"block w-full text-center py-3 px-6 rounded-xl font-semibold transition-all duration-300 \".concat(tier.name === 'Enterprise' ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 shadow-lg hover:shadow-xl' : tier.popular ? 'bg-gradient-to-r from-orange-500 to-orange-600 text-white hover:from-orange-600 hover:to-orange-700 shadow-lg hover:shadow-xl' : 'bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 shadow-lg hover:shadow-xl'),\n                                                children: tier.cta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, tier.name, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    className: \"text-center mb-16\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold text-white mb-4\",\n                                        children: \"Looking for something else?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.1\n                                            },\n                                            className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-white mb-3\",\n                                                    children: \"BYOK Framework\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 text-sm mb-4\",\n                                                    children: \"Bring Your Own Keys - maintain complete control over your API costs.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-xs mb-4\",\n                                                    children: \"RouKey never marks up your API costs. You pay providers directly while getting intelligent routing and cost optimization features.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/docs\",\n                                                    className: \"inline-flex items-center text-orange-400 hover:text-orange-300 text-sm font-medium transition-colors\",\n                                                    children: \"Learn more\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.2\n                                            },\n                                            className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-white mb-3\",\n                                                    children: \"Intelligent Routing\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 text-sm mb-4\",\n                                                    children: \"Smart AI model selection based on complexity, cost, and performance requirements.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-xs mb-4\",\n                                                    children: \"Automatically route simple tasks to cost-effective models and complex tasks to premium models for optimal performance and cost savings.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/features\",\n                                                    className: \"inline-flex items-center text-orange-400 hover:text-orange-300 text-sm font-medium transition-colors\",\n                                                    children: \"Learn more\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.3\n                                            },\n                                            className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-white mb-3\",\n                                                    children: \"Smart Cost Optimization\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 text-sm mb-4\",\n                                                    children: \"Save up to 70% on AI costs with intelligent routing strategies.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-xs mb-4\",\n                                                    children: \"Route simple tasks to cheaper models and complex tasks to premium models automatically.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/routing-strategies\",\n                                                    className: \"inline-flex items-center text-orange-400 hover:text-orange-300 text-sm font-medium transition-colors\",\n                                                    children: \"View strategies\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    className: \"text-center mb-16\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold text-white mb-4\",\n                                        children: \"What's included?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"overflow-x-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                    className: \"bg-white/5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-semibold text-white\",\n                                                                children: \"Features\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 459,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-center text-sm font-semibold text-white\",\n                                                                children: \"Free\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-center text-sm font-semibold text-white\",\n                                                                children: \"Starter\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-center text-sm font-semibold text-white bg-orange-500/10\",\n                                                                children: \"Pro\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                    className: \"divide-y divide-white/10\",\n                                                    children: comparisonFeatures.map((category, categoryIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    className: \"bg-white/5\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        colSpan: 4,\n                                                                        className: \"px-6 py-3 text-sm font-semibold text-white\",\n                                                                        children: category.category\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                        lineNumber: 469,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                    lineNumber: 468,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                category.features.map((feature, featureIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        className: \"hover:bg-white/5\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 text-sm text-gray-300\",\n                                                                                children: feature.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                                lineNumber: 475,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 text-sm text-center\",\n                                                                                children: renderFeatureValue(feature.free)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                                lineNumber: 476,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 text-sm text-center\",\n                                                                                children: renderFeatureValue(feature.starter)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                                lineNumber: 477,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 text-sm text-center bg-orange-500/5\",\n                                                                                children: renderFeatureValue(feature.pro)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                                lineNumber: 478,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, \"\".concat(categoryIndex, \"-\").concat(featureIndex), true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                        lineNumber: 474,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            ]\n                                                        }, category.category, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                            lineNumber: 444,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                        lineNumber: 443,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    className: \"text-left mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-6 h-6 rounded bg-orange-500/20 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-orange-400 text-xs font-bold\",\n                                                        children: \"?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-orange-400 text-sm font-medium\",\n                                                    children: \"FAQs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold text-white mb-4\",\n                                            children: \"Frequently asked questions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        {\n                                            question: \"How does RouKey's BYOK model work?\",\n                                            answer: \"With Bring Your Own Keys (BYOK), you provide your own API keys from providers like OpenAI, Anthropic, Google, etc. RouKey intelligently routes your requests to the optimal model while you pay providers directly. No markup, complete cost transparency, and you maintain full control over your API spending.\"\n                                        },\n                                        {\n                                            question: \"What makes RouKey different from other AI gateways?\",\n                                            answer: \"RouKey is the only AI gateway with intelligent multi-agent orchestration, proprietary AI classification for role-based routing, and advanced cost optimization. Unlike simple proxies, RouKey uses its proprietary orchestration engine for complex workflows and can save up to 70% on AI costs through smart routing strategies.\"\n                                        },\n                                        {\n                                            question: \"Can I cancel my subscription at any time?\",\n                                            answer: \"Yes, you can cancel your RouKey subscription at any time through your account settings. Your access will continue until the end of your current billing period, and you won't be charged for the next cycle. Your configurations and API keys remain accessible during the billing period.\"\n                                        },\n                                        {\n                                            question: \"How secure are my API keys with RouKey?\",\n                                            answer: \"Your API keys are encrypted using enterprise-grade AES-256 encryption and stored securely. RouKey follows a BYOK model, meaning you maintain control of your keys. We never store or log your actual AI responses, and all communications are encrypted in transit with SOC 2 compliance.\"\n                                        },\n                                        {\n                                            question: \"What routing strategies does RouKey support?\",\n                                            answer: \"RouKey offers multiple intelligent routing strategies: Intelligent Role Routing (AI-powered classification), Complexity-Based Routing (cost optimization), Strict Fallback (ordered failover), Smart Cost Optimization, and A/B Testing. Each strategy is designed for different use cases and optimization goals.\"\n                                        },\n                                        {\n                                            question: \"Do I need technical knowledge to use RouKey?\",\n                                            answer: \"RouKey is designed for developers but offers different complexity levels. The Free plan provides simple fallback routing that requires minimal setup. Advanced features like multi-agent workflows and custom roles are available for teams that need sophisticated AI orchestration.\"\n                                        },\n                                        {\n                                            question: \"What's included in the multi-agent workflows?\",\n                                            answer: \"RouKey's multi-agent workflows use our proprietary orchestration engine for advanced coordination including sequential workflows, parallel execution, supervisor patterns, and hierarchical coordination. Features include memory persistence, real-time streaming, tool calling, and comprehensive error handling for complex AI tasks.\"\n                                        },\n                                        {\n                                            question: \"How does RouKey handle rate limits and failures?\",\n                                            answer: \"RouKey automatically handles rate limits and API failures through intelligent fallback mechanisms. When one provider hits limits or fails, RouKey seamlessly routes to alternative models. This ensures high availability and reduces the impact of individual provider issues on your applications.\"\n                                        }\n                                    ].map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: index * 0.1\n                                            },\n                                            className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 hover:bg-white/10 transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-white pr-4\",\n                                                            children: faq.question\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-orange-400 text-xl font-light\",\n                                                            children: \"+\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 text-sm mt-4 leading-relaxed\",\n                                                    children: faq.answer\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                        lineNumber: 491,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                lineNumber: 562,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n        lineNumber: 184,\n        columnNumber: 5\n    }, this);\n}\n_s(PricingPageContent, \"6497yMqPGgGxY65KeucnntnPwNY=\");\n_c = PricingPageContent;\nfunction PricingPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            style: {\n                background: \"radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                        lineNumber: 574,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300\",\n                        children: \"Loading pricing...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                        lineNumber: 575,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                lineNumber: 573,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n            lineNumber: 570,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PricingPageContent, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n            lineNumber: 579,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n        lineNumber: 569,\n        columnNumber: 5\n    }, this);\n}\n_c1 = PricingPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"PricingPageContent\");\n$RefreshReg$(_c1, \"PricingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/pricing/page.tsx\n"));

/***/ })

});